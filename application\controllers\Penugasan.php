<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Operators $operators
 * @property Task $task
 * @property CI_Upload $upload
 * @property MsUsers $msusers
 * @property TaskHistory $taskhistory
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property Jabatans $jabatans
 * @property TaskCollect $taskcollect
 * @property Villages $villages
 * @property TaskDocument $taskdocument
 * @property Datatables $datatables
 */
class Penugasan extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Operators', 'operators');
        $this->load->model('Task', 'task');
        $this->load->model('TaskHistory', 'taskhistory');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('Jabatans', 'jabatans');
        $this->load->model('TaskCollect', 'taskcollect');
        $this->load->model('Villages', 'villages');
        $this->load->model('TaskDocument', 'taskdocument');
    }

    public function index($type = null)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $currentuser = getCurrentUser();

        $data = array();
        $data['title'] = 'Penugasan';
        $data['content'] = 'penugasan/index';

        if (isKecamatan()) {
            $data['village'] = $this->msusers->result(array(
                'a.role' => 'Villages',
                'a.kecamatanid' => $currentuser->kecamatanid
            ));
        } else if (isPMD()) {
            $data['kecamatan'] = $this->msusers->result(array(
                'a.role' => 'Kecamatan',
                'a.kabkotaid' => $currentuser->kabkotaid
            ));
            $data['village'] = $this->msusers->result(array(
                'a.role' => 'Villages',
                'a.kabkotaid' => $currentuser->kabkotaid
            ));
        }

        $data['type'] = $type;

        return $this->load->view('master', $data);
    }

    public function datatables($type = null)
    {
        $kecamatanid = getPost('kecamatanid');
        $villageid = getPost('villageid');
        $createdby = getPost('createdby');
        $startdate = getPost('startdate');
        $enddate = getPost('enddate');
        $taskfor = getPost('taskfor');

        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $currentuser = getCurrentUser();
        $where = array();

        if ($type == 'pmd') {
            $where['d.role'] = 'DPMD';
        } else if ($type == 'kecamatan') {
            $where['d.role'] = 'Kecamatan';
        } else if ($type == 'desa') {
            $where['d.role'] = 'Villages';
        }

        if ($taskfor != null) {
            $where['a.taskfor'] = $taskfor;
        }

        if (isVillages()) {
            $currentdesaid = getCurrentUser()->desaid;
            $currentiduser = getCurrentIdUser();
            $where["(a.createdby = '$currentiduser' OR b.desaid = '$currentdesaid') ="] = true;
        } elseif (isOperator()) {
            $currentkabkotaid = $currentuser->kabkotaid;
            $currentkecamatanid = $currentuser->kecamatanid;
            $currentdesaid = $currentuser->desaid;

            $villageuser = $this->msusers->get(array(
                'a.desaid' => $currentuser->desaid,
                'a.role' => 'Villages'
            ))->row();

            $where['a.taskfor'] = 'Operator Desa';
            $where['a.to'] = $currentuser->id;
            $where["((d.role = 'DPMD' AND d.kabkotaid = '$currentkabkotaid' AND a.is_release = 1) OR (d.role = 'Kecamatan' AND d.kecamatanid = '$currentkecamatanid' AND a.is_release = 1) OR (d.role = 'Villages' AND d.desaid = '$currentdesaid' AND a.is_release = 1)) ="] = true;
        } elseif (isKecamatan()) {
            $currentkabkotaid = getCurrentUser()->kabkotaid;
            $currentkecamatanid = getCurrentUser()->kecamatanid;
            $currentiduser = getCurrentIdUser();

            $where["((d.kabkotaid = '$currentkabkotaid' AND d.kecamatanid = '$currentkecamatanid' AND a.is_release = 1) OR (d.kecamatanid = '$currentkecamatanid' AND a.is_release = 1) OR a.createdby = '$currentiduser' OR (d.role = 'DPMD' AND d.kabkotaid = '$currentkabkotaid' AND b.kecamatanid = '$currentkecamatanid' AND a.is_release = 1)) ="] = true;
        } elseif (isPMD()) {
            $currentkabkotaid = getCurrentUser()->kabkotaid;
            $currentiduser = getCurrentIdUser();

            $where["((d.kabkotaid = '$currentkabkotaid' AND a.is_release = 1) OR a.createdby = '$currentiduser') ="] = true;
            $where['a.parentid'] = null;
        } elseif (isKepalaDesa()) {
            $where['b.desaid'] = getCurrentUser()->desaid;
        } else if (isBPD()) {
            $currentkabkotaid = $currentuser->kabkotaid;
            $currentkecamatanid = $currentuser->kecamatanid;
            $currentdesaid = $currentuser->desaid;

            $where["((d.role = 'DPMD' AND d.kabkotaid = '$currentkabkotaid' AND a.is_release = 1) OR (d.role = 'Kecamatan' AND d.kecamatanid = '$currentkecamatanid' AND a.is_release = 1) OR (d.role = 'Villages' AND d.desaid = '$currentdesaid' AND a.is_release = 1)) ="] = true;
            $where['a.to'] = getCurrentIdUser();
            $where["(a.processby IS NULL OR a.processby = '$currentuser->id') ="] = true;
        }

        if ($kecamatanid != null) {
            $where['b.kecamatanid'] = $kecamatanid;
        }

        if ($villageid != null) {
            $villageuser = getCurrentUser($villageid);
            $where['b.desaid'] = $villageuser->desaid;
        }

        if ($createdby != null) {
            $where['d.role'] = $createdby;
        }

        if ($startdate != null) {
            $where['DATE(a.starttask) >='] = date('Y-m-d', strtotime($startdate));
        }

        if ($enddate != null) {
            $where['DATE(a.starttask) <='] = date('Y-m-d', strtotime($enddate));
        }

        $datatables = $this->datatables->make('Task', 'QueryDatatables', 'SearchDatatables');
        $data = array();

        foreach ($datatables->getData($where) as $key => $value) {
            $taskcollect = $this->db->get_where('taskcollect', ['taskid' => $value->id])->result();
            $children = $this->db->get_where('task', ['parentid' => $value->id])->result();
            $taskdocument = $this->db->get_where('taskdocument', ['taskid' => $value->id])->result();

            if ($value->created_role == 'DPMD' || $value->created_role == 'Kecamatan') {
                if ($value->created_role == 'DPMD') {
                    $total_villages = getTotalVillagesByCity($value->kabkotaname);
                } else {
                    $total_villages = getTotalVillagesByDistrict($value->kecamatanname);
                }

                $total_process = $this->db->select('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->join('msusers b', 'b.id = a.to')
                    ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->get_where('task a', [
                        'a.parentid' => $value->id,
                        'a.status' => 'Process'
                    ])
                    ->num_rows();
                $total_processing = $this->db->select('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->join('msusers b', 'b.id = a.to')
                    ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->get_where('task a', [
                        'a.parentid' => $value->id,
                        'a.status' => 'Processing'
                    ])
                    ->num_rows();
                $total_pending = $this->db->select('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->join('msusers b', 'b.id = a.to')
                    ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->get_where('task a', [
                        'a.parentid' => $value->id,
                        'a.status' => 'Pending'
                    ])
                    ->num_rows();
                $total_done = $this->db->select('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->join('msusers b', 'b.id = a.to')
                    ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->get_where('task a', [
                        'a.parentid' => $value->id,
                        'a.status' => 'Done'
                    ])
                    ->num_rows();
                $total_reject = $this->db->select('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->join('msusers b', 'b.id = a.to')
                    ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                    ->get_where('task a', [
                        'a.parentid' => $value->id,
                        'a.status' => 'Reject'
                    ])
                    ->num_rows();

                $total_process = ($total_villages - $total_process) + $total_process - $total_processing - $total_pending - $total_done - $total_reject;
            } else {
                $total_process = $this->db->get_where('task', ['parentid' => $value->id, 'status' => 'Process'])->num_rows();
                $total_processing = $this->db->get_where('task', ['parentid' => $value->id, 'status' => 'Processing'])->num_rows();
                $total_pending = $this->db->get_where('task', ['parentid' => $value->id, 'status' => 'Pending'])->num_rows();
                $total_done = $this->db->get_where('task', ['parentid' => $value->id, 'status' => 'Done'])->num_rows();
                $total_reject = $this->db->get_where('task', ['parentid' => $value->id, 'status' => 'Reject'])->num_rows();
            }

            $actions = "<button type=\"button\" class=\"btn btn-primary btn-sm mb-1 me-1\" onclick=\"historyTask($value->id)\">
                <i class=\"fa fa-history\"></i>
                History
            </button>";

            $start_number_document = 1;

            if ($value->document_sample != null && file_exists('uploads/' . $value->document_sample)) {
                $actions .= "<a href=\"" . base_url('uploads/' . $value->document_sample) . "\" class=\"btn btn-warning btn-sm mb-1 me-1\" target=\"_blank\">
                    <i class=\"fa fa-download\"></i>
                    Download Dokumen $start_number_document
                </a>";

                $start_number_document++;
            }

            if (count($taskdocument) > 0) {
                $taskdocument_split = array_chunk($taskdocument, 5);

                $actions .= "<div class=\"d-flex\">";
                foreach ($taskdocument_split as $k => $v) {
                    $actions .= "<ul>";
                    foreach ($v as $k2 => $v2) {
                        $actions .= "<li>
                            <a href=\"" . base_url('uploads/' . $v2->document) . "\" class=\"btn btn-warning btn-sm mb-1 me-1\" target=\"_blank\">
                                <i class=\"fa fa-download\"></i>
                                Download Dokumen $start_number_document " . (!empty($v2->description) ? "( $v2->description )" : null) . "
                            </a>
                        </li>";

                        $start_number_document++;
                    }
                    $actions .= "</ul>";
                }
                $actions .= "</div>";
            }

            if (count($children) > 0) {
                $actions .= " <a href=\"" . base_url(uri_string() . '/../detail/' . $value->id) . "\" class=\"btn btn-primary btn-sm mb-1 me-1\">
                    <i class=\"fa fa-eye\"></i>
                    Detail
                </a>";
            }

            if ($value->status == 'Process' && isKepalaDesa() && $value->taskfor == 'Operator Desa') {
                $actions .= "<button type=\"button\" class=\"btn btn-danger btn-sm mb-1 me-1\" onclick=\"switchTask($value->id)\">
                    <i class=\"fa fa-arrow-right\"></i>
                    Alihkan Tugas
                </button>";
            }

            if ($value->is_release == null && !isOperator()) {
                $actions .= "<button type=\"button\" class=\"btn btn-success btn-sm mb-1 me-1\" onclick=\"releaseTask($value->id)\">
                    <i class=\"fa fa-lock\"></i>
                    Release
                </button>

                <a href=\"" . base_url(uri_string() . '/../edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm mb-1 me-1\">
                    <i class=\"fa fa-edit\"></i>
                </a>";
            } elseif ($value->status == 'Process' || $value->status == 'Processing' || $value->status == 'Reject') {
                if (isOperator() || isBPD()) {
                    if ($value->status == 'Processing' || $value->status == 'Reject') {
                        $actions .= "<button type=\"button\" class=\"btn btn-primary btn-sm mb-1 me-1\" onclick=\"collectTask($value->id)\">
                            <i class=\"fa fa-paper-plane\"></i>
                            Kumpulkan " . ($value->status == 'Reject' ? 'Ulang' : null) . "
                        </button>";
                    } elseif ($value->status == 'Process') {
                        $actions .= "<button type=\"button\" class=\"btn btn-primary btn-sm mb-1 me-1\" onclick=\"processTask($value->id)\">
                            <i class=\"fa fa-check\"></i>
                            Proses Pengerjaan
                        </button>";
                    }
                }
            } elseif ($value->status == 'Pending' || $value->status == 'Done') {
                if (count($taskcollect) == 0 && $value->document != null) {
                    $actions .= "<a href=\"" . base_url('uploads/' . $value->document) . "\" class=\"btn btn-warning btn-sm mb-1 me-1\" target=\"_blank\">
                        <i class=\"fa fa-download\"></i>
                        Download Dokumen
                    </a>";
                } else {
                    foreach ($taskcollect as $k => $v) {
                        $actions .= "<a href=\"" . base_url('uploads/' . $v->document) . "\" class=\"btn btn-warning btn-sm mb-1 me-1\" target=\"_blank\">
                            <i class=\"fa fa-download\"></i>
                            Download Dokumen " . ($k + 1) . "
                        </a>";
                    }
                }

                if (((isVillages() || isKecamatan() || isPMD() || isKepalaDesa()) && $value->status == 'Pending' && $value->created_role != 'PMD') || ($value->created_role == 'PMD' && (isKecamatan() || isPMD())) && $value->status == 'Pending') {
                    $actions .= "<button type=\"button\" class=\"btn btn-success btn-sm mb-1 me-1\" onclick=\"verifyTask($value->id)\">
                        <i class=\"fa fa-check\"></i>
                        Verifikasi
                    </button>

                    <button type=\"button\" class=\"btn btn-danger btn-sm mb-1 me-1\" onclick=\"rejectTask($value->id)\">
                        <i class=\"fa fa-times\"></i>
                        Tolak
                    </button>";
                }
            }

            if (!isBPD() && !isOperator()) {
                $actions .= "<button type=\"button\" class=\"btn btn-danger btn-sm mb-1 me-1\" onclick=\"deleteData('$value->task', $value->id)\">
                    <i class=\"fa fa-trash\"></i>
                </button>";
            }

            $taskfor_detail = $value->taskfor ?? 'Pemerintah Desa';

            if ($value->created_role == 'DPMD' && $value->parentid == null) {
                if ($value->taskfor == 'Operator Desa') {
                    $taskfor_detail .= ' - Jabatan: ' . $value->positionname;
                }
            } elseif ($value->created_role == 'Kecamatan' && $value->parentid == null) {
                if ($value->taskfor == 'Operator Desa') {
                    $taskfor_detail .= ' - Jabatan: ' . $value->positionname;
                }
            } else {
                if ($value->operator_name != null) {
                    if ($value->positionname == null) {
                        $taskfor_detail .= ' - ' . $value->operator_name;
                    } else {
                        $taskfor_detail .= ' - Jabatan: ' . $value->positionname . ' - ' . $value->operator_name;
                    }
                }
            }

            $status = "";
            if ($value->parentid != null || ($value->created_role == 'Villages')) {
                if ($value->status == null && $value->is_release == null) {
                    $status = "<span class=\"badge bg-danger\">Menunggu Proses Release</span>";
                } elseif ($value->status == 'Process') {
                    $status = "<span class=\"badge bg-warning\">Belum Mengerjakan</span>";
                } elseif ($value->status == 'Processing') {
                    $status = "<span class=\"badge bg-primary\">Sedang Mengerjakan</span>";
                } elseif ($value->status == 'Pending') {
                    $status = "<span class=\"badge bg-secondary\">Menunggu Verifikasi</span>";
                } elseif ($value->status == 'Done') {
                    $status = "<span class=\"badge bg-success\">Sudah Mengerjakan</span>";
                } elseif ($value->status == 'Reject') {
                    $status = "<span class=\"badge bg-danger\">Ditolak - $value->reason</span>";
                }
            } else {
                if ($value->status == null && $value->is_release == null) {
                    $status = "<span class=\"badge bg-danger\">Menunggu Proses Release</span>";
                } else {
                    if ($total_process > 0) {
                        $status .= "<a href=\"javascript:;\" onclick=\"detailStatus($value->id, 'Process')\">
                            <span class=\"badge bg-warning mb-1\">$total_process " . ($value->created_role == 'DPMD' || $value->created_role == 'Kecamatan' ? 'Desa' : 'Tugas') . " Belum Mengerjakan</span>
                        </a>";
                    }

                    if ($total_processing > 0) {
                        $status .= "<a href=\"javascript:;\" onclick=\"detailStatus($value->id, 'Processing')\">
                            <span class=\"badge bg-primary mb-1\">$total_processing " . ($value->created_role == 'DPMD' || $value->created_role == 'Kecamatan' ? 'Desa' : 'Tugas') . " Sedang Mengerjakan</span>
                        </a>";
                    }

                    if ($total_pending > 0) {
                        $status .= "<a href=\"javascript:;\" onclick=\"detailStatus($value->id, 'Pending')\">
                            <span class=\"badge bg-secondary mb-1\">$total_pending " . ($value->created_role == 'DPMD' || $value->created_role == 'Kecamatan' ? 'Desa' : 'Tugas') . " Menunggu Verifikasi</span>
                        </a>";
                    }

                    if ($total_done > 0) {
                        $status .= "<a href=\"javascript:;\" onclick=\"detailStatus($value->id, 'Done')\">
                            <span class=\"badge bg-success mb-1\">$total_done " . ($value->created_role == 'DPMD' || $value->created_role == 'Kecamatan' ? 'Desa' : 'Tugas') . " Sudah Mengerjakan</span>
                        </a>";
                    }

                    if ($total_reject > 0) {
                        $status .= "<a href=\"javascript:;\" onclick=\"detailStatus($value->id, 'Reject')\">
                            <span class=\"badge bg-danger mb-1\">$total_reject " . ($value->created_role == 'DPMD' || $value->created_role == 'Kecamatan' ? 'Desa' : 'Tugas') . " Ditolak</span>
                        </a>";
                    }
                }
            }

            $detail = array();
            $detail[] = $taskfor_detail;
            $detail[] = date('d F Y', strtotime($value->deadline));
            $detail[] = $value->operator_kabkotaname;
            $detail[] = $value->operator_kecamatanname;
            $detail[] = $value->operator_desaname;
            $detail[] = $value->starttask ? date('d F Y H:i:s', strtotime($value->starttask)) : '-';
            $detail[] = $value->task;
            $detail[] = $value->description;
            $detail[] = $status;
            $detail[] = $value->created_role . ' - ' . ($value->created_role == 'Kecamatan' ? ucwords(strtolower($value->kecamatanname ?? '')) : ($value->created_role == 'DPMD' ? ucwords(strtolower($value->kabkotaname ?? '')) : ucwords(strtolower($value->desaname ?? ''))));
            $detail[] = $value->verify_role ?? '-';
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $currentuser = getCurrentUser();

        $data = array();
        $data['title'] = 'Tambah Penugasan';
        $data['content'] = 'penugasan/add';
        $data['operators'] = $this->operators->select('a.*, IFNULL(b.name, a.position) as positionname')
            ->join('jabatan b', 'b.id = a.positionid', 'LEFT')
            ->result(array(
                'a.role' => 'Operator',
                'a.desaid' => $currentuser->desaid
            ));
        $data['position'] = $this->jabatans->order_by('ordering', 'ASC')->result(array(
            'type' => 'Operator Desa'
        ));

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $starttask = getPost('starttask');
        $deadline = getPost('deadline');
        $task = getPost('task');
        $to = getPost('to');
        $description = getPost('description');
        $taskfor = getPost('taskfor');

        if ($deadline == null) {
            return JSONResponseDefault('FAILED', 'Deadline tidak boleh kosong!');
        } else if ($task == null) {
            return JSONResponseDefault('FAILED', 'Tugas tidak boleh kosong!');
        } else if ($taskfor == null) {
            return JSONResponseDefault('FAILED', 'Tugas untuk tidak boleh kosong!');
        } else if (!in_array($taskfor, array('Operator Desa', 'BPD'))) {
            return JSONResponseDefault('FAILED', 'Tugas untuk tidak valid!');
        } else if ((!isPMD() && !isKecamatan()) && $taskfor != 'Operator Desa') {
            return JSONResponseDefault('FAILED', 'Tugas untuk tidak valid!');
        } else if ($to == null && $taskfor == 'Operator Desa') {
            return JSONResponseDefault('FAILED', 'Ditugaskan kepada tidak boleh kosong!');
        } else if ($description == null) {
            return JSONResponseDefault('FAILED', 'Keterangan tidak boleh kosong!');
        }

        if (!isPMD() && !isKecamatan()) {
            $get_to = $this->operators->get(array(
                'id' => $to
            ));

            if ($get_to->num_rows() == 0) {
                return JSONResponseDefault('FAILED', 'Operator tidak ditemukan!');
            }
        } else {
            if ($taskfor == 'Operator Desa') {
                $get_to = $this->jabatans->get(array(
                    'id' => $to
                ));

                if ($get_to->num_rows() == 0) {
                    return JSONResponseDefault('FAILED', 'Jabatan tidak ditemukan!');
                }
            }
        }

        $insert = array();
        $insert['starttask'] = $starttask;
        $insert['deadline'] = $deadline;
        $insert['task'] = $task;
        $insert['to'] = $to;
        $insert['description'] = $description;
        $insert['createddate'] = date('Y-m-d H:i:s');
        $insert['createdby'] = getCurrentIdUser();
        $insert['taskfor'] = $taskfor;

        $this->task->insert($insert);
        $taskid = $this->db->insert_id();

        $documents = isset($_FILES['document']['size'][0]) ? $_FILES['document'] : null;
        if ($documents != null) {
            $config = array();
            $config['upload_path'] = 'uploads/';
            $config['allowed_types'] = 'pdf|doc|docx|xls|xlsx|png|jpeg|jpg|zip|rar';
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            foreach ($documents['name'] as $key => $value) {
                if ($documents['size'][$key] == 0) continue;

                $_FILES['userfile']['name'] = $documents['name'][$key];
                $_FILES['userfile']['type'] = $documents['type'][$key];
                $_FILES['userfile']['tmp_name'] = $documents['tmp_name'][$key];
                $_FILES['userfile']['error'] = $documents['error'][$key];
                $_FILES['userfile']['size'] = $documents['size'][$key];

                if (!$this->upload->do_upload('userfile')) {
                    return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
                }

                $insert_document = array();
                $insert_document['taskid'] = $taskid;
                $insert_document['document'] = $this->upload->data('file_name');
                $insert_document['description'] = getPost('note')[$key] ?? null;
                $insert_document['createddate'] = getCurrentDate();
                $insert_document['createdby'] = getCurrentIdUser();

                $this->taskdocument->insert($insert_document);
            }
        }

        $insert = array();
        $insert['taskid'] = $taskid;
        if ((isPMD() || isKecamatan()) && $taskfor == 'Operator Desa') {
            $insert['activity'] = 'Menambahkan tugas kepada Seluruh ' . $get_to->row()->name . ' dengan detail sebagai berikut: ' . $task . ' dengan deadline ' . date('d F Y', strtotime($deadline)) . ' dan keterangan ' . $description;
        } else if ($taskfor == 'Operator Desa') {
            $insert['activity'] = 'Menambahkan tugas kepada ' . $get_to->row()->name . ' dengan detail sebagai berikut: ' . $task . ' dengan deadline ' . date('d F Y', strtotime($deadline)) . ' dan keterangan ' . $description;
        } else if ($taskfor == 'BPD') {
            $insert['activity'] = 'Menambahkan tugas kepada Seluruh BPD dengan detail sebagai berikut: ' . $task . ' dengan deadline ' . date('d F Y', strtotime($deadline)) . ' dan keterangan ' . $description;
        }
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->taskhistory->insert($insert);

        return JSONResponseDefault('OK', 'Penugasan berhasil disimpan!');
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $get = $this->task->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('penugasan'));
        }

        $data = array();
        $data['title'] = 'Edit Penugasan';
        $data['content'] = 'penugasan/edit';
        $data['operators'] = $this->operators->result(array(
            'role' => 'Operator'
        ));
        $data['task'] = $get->row();
        $data['position'] = $this->jabatans->order_by('ordering', 'ASC')->result(array(
            'type' => 'Operator Desa'
        ));
        $data['task_document'] = $this->taskdocument->result(array(
            'taskid' => $id
        ));

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $get = $this->task->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penugasan tidak ditemukan!');
        }

        $starttask = getPost('starttask');
        $deadline = getPost('deadline');
        $task = getPost('task');
        $to = getPost('to');
        $description = getPost('description');
        $taskfor = getPost('taskfor');
        $document_id = getPost('document_id', array());

        if ($deadline == null) {
            return JSONResponseDefault('FAILED', 'Deadline tidak boleh kosong!');
        } else if ($task == null) {
            return JSONResponseDefault('FAILED', 'Tugas tidak boleh kosong!');
        } else if ($taskfor == null) {
            return JSONResponseDefault('FAILED', 'Tugas untuk tidak boleh kosong!');
        } else if (!in_array($taskfor, array('Operator Desa', 'BPD'))) {
            return JSONResponseDefault('FAILED', 'Tugas untuk tidak valid!');
        } else if ((!isPMD() && !isKecamatan()) && $taskfor != 'Operator Desa') {
            return JSONResponseDefault('FAILED', 'Tugas untuk tidak valid!');
        } else if ($to == null && $taskfor == 'Operator Desa') {
            return JSONResponseDefault('FAILED', 'Ditugaskan kepada tidak boleh kosong!');
        } else if ($description == null) {
            return JSONResponseDefault('FAILED', 'Keterangan tidak boleh kosong!');
        }

        if (!isPMD() && !isKecamatan()) {
            $get_to = $this->operators->get(array(
                'id' => $to
            ));

            if ($get_to->num_rows() == 0) {
                return JSONResponseDefault('FAILED', 'Operator tidak ditemukan!');
            }
        } else {
            if ($taskfor == 'Operator Desa') {
                $get_to = $this->jabatans->get(array(
                    'id' => $to
                ));

                if ($get_to->num_rows() == 0) {
                    return JSONResponseDefault('FAILED', 'Jabatan tidak ditemukan!');
                }
            }
        }

        $update = array();
        $update['starttask'] = $starttask;
        $update['deadline'] = $deadline;
        $update['task'] = $task;
        $update['to'] = $to;
        $update['description'] = $description;
        $update['taskfor'] = $taskfor;

        $this->task->update(array(
            'id' => $id
        ), $update);

        $task_document = $this->taskdocument->result(array(
            'taskid' => $id
        ));

        foreach ($task_document as $key => $value) {
            if (!in_array($value->id, $document_id)) {
                $this->taskdocument->delete(array('taskid' => $id));
            }
        }

        $documents = isset($_FILES['document']['size'][0]) ? $_FILES['document'] : null;
        if ($documents != null) {
            $config = array();
            $config['upload_path'] = 'uploads/';
            $config['allowed_types'] = 'pdf|doc|docx|xls|xlsx|png|jpeg|jpg|zip|rar';
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            foreach ($documents['name'] as $key => $value) {
                if ($documents['size'][$key] == 0) continue;

                $_FILES['userfile']['name'] = $documents['name'][$key];
                $_FILES['userfile']['type'] = $documents['type'][$key];
                $_FILES['userfile']['tmp_name'] = $documents['tmp_name'][$key];
                $_FILES['userfile']['error'] = $documents['error'][$key];
                $_FILES['userfile']['size'] = $documents['size'][$key];

                if (!$this->upload->do_upload('userfile')) {
                    return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
                }

                $insert_document = array();
                $insert_document['taskid'] = $id;
                $insert_document['document'] = $this->upload->data('file_name');
                $insert_document['description'] = getPost('note')[$key] ?? null;
                $insert_document['createddate'] = getCurrentDate();
                $insert_document['createdby'] = getCurrentIdUser();

                $this->taskdocument->insert($insert_document);
            }
        }

        $insert = array();
        $insert['taskid'] = $id;
        if ((isPMD() || isKecamatan()) && $taskfor == 'Operator Desa') {
            $insert['activity'] = 'Mengubah tugas kepada Seluruh ' . $get_to->row()->name . ' dengan detail sebagai berikut: ' . $task . ' dengan deadline ' . date('d F Y', strtotime($deadline)) . ' dan keterangan ' . $description;
        } elseif ($taskfor == 'Operator Desa') {
            $insert['activity'] = 'Mengubah tugas kepada ' . $get_to->row()->name . ' dengan detail sebagai berikut: ' . $task . ' dengan deadline ' . date('d F Y', strtotime($deadline)) . ' dan keterangan ' . $description;
        } elseif ($taskfor == 'BPD') {
            $insert['activity'] = 'Mengubah tugas kepada Seluruh BPD dengan detail sebagai berikut: ' . $task . ' dengan deadline ' . date('d F Y', strtotime($deadline)) . ' dan keterangan ' . $description;
        }
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->taskhistory->insert($insert);

        return JSONResponseDefault('OK', 'Penugasan berhasil diubah!');
    }

    public function process_delete()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('ID tidak boleh kosong');
            }

            $get = $this->task->get(array(
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Penugasan tidak ditemukan');
            }

            $row = $get->row();

            $this->task->delete(array(
                'id' => $id
            ));

            $this->task->delete(array(
                'parentid' => $id
            ));

            insertLogactivity('delete', 'Menghapus data penugasan ' . $row->task, $row);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dihapus');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Penugasan berhasil dihapus!');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_release()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('ID tidak boleh kosong!');
            }

            $get = $this->task->select('a.*, b.role AS created_role, b.kabkotaid AS created_kabkotaid, b.kecamatanid AS created_kecamatanid')
                ->join('msusers b', 'b.id = a.createdby')
                ->get(array(
                    'a.id' => $id,
                    'a.is_release' => null,
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Penugasan tidak ditemukan!');
            }

            $row = $get->row();

            if ($row->created_role != 'DPMD' && $row->created_role != 'Kecamatan') {
                $operator = $this->msusers->get(array(
                    'id' => $row->to
                ))->row();

                $message = "Hallo, " . $operator->name . "!\n\n";
                $message .= "Anda mendapatkan penugasan baru dengan detail sebagai berikut:\n\n";
                $message .= "Tugas: " . $row->task . "\n";
                $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
                $message .= "Keterangan: " . $row->description . "\n\n";
                $message .= "Silahkan login ke aplikasi untuk melihat detail penugasan.\n\n";
                $message .= "Silahkan klik link berikut untuk login: " . base_url('auth/login') . "\n\n";
                $message .= "Terima kasih.";

                sendWABlas($operator->phonenumber, $message);

                $kepala_desa = $this->msusers->get(array(
                    'role' => 'Kepala Desa',
                    'desaid' => $operator->desaid
                ))->row();

                if ($kepala_desa != null) {
                    // hanya mengirimkan pesan notifikasi, tidak menyuruh untuk verifikasi
                    $message = "Hallo, " . $kepala_desa->name . "!\n\n";
                    $message .= "Operator " . $operator->name . " telah mendapatkan penugasan baru dengan detail sebagai berikut:\n\n";
                    $message .= "Tugas: " . $row->task . "\n";
                    $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
                    $message .= "Keterangan: " . $row->description . "\n\n";
                    $message .= "Silahkan login ke aplikasi untuk melihat detail penugasan.\n\n";
                    $message .= "Silahkan klik link berikut untuk login: " . base_url('auth/login') . "\n\n";
                    $message .= "Terima kasih.";

                    sendWABlas($kepala_desa->phonenumber, $message);
                }
            } else {
                if ($row->taskfor == 'Operator Desa') {
                    $to = $row->to; // positionid

                    if ($row->created_role == 'DPMD') {
                        $get_operator = $this->operators->result(array(
                            'positionid' => $to,
                            'kabkotaid' => $row->created_kabkotaid
                        ));
                    } else if ($row->created_role == 'Kecamatan') {
                        $get_operator = $this->operators->result(array(
                            'positionid' => $to,
                            'kabkotaid' => $row->created_kabkotaid,
                            'kecamatanid' => $row->created_kecamatanid
                        ));
                    }

                    $get_position = $this->jabatans->get(array('id' => $to));

                    $desaid = array();
                    foreach ($get_operator as $operator) {
                        $insert = array();
                        $insert['deadline'] = $row->deadline;
                        $insert['task'] = $row->task;
                        $insert['to'] = $operator->id;
                        $insert['description'] = $row->description;
                        $insert['createddate'] = date('Y-m-d H:i:s');
                        $insert['createdby'] = getCurrentIdUser();
                        $insert['taskfor'] = 'Operator Desa';
                        $insert['is_release'] = 1;
                        $insert['status'] = 'Process';
                        $insert['parentid'] = $id;
                        $insert['starttask'] = $row->starttask;
                        $insert['document_sample'] = $row->document_sample;

                        $this->task->insert($insert);
                        $loops_task_id = $this->db->insert_id();

                        $task_document = $this->taskdocument->result(array(
                            'taskid' => $id
                        ));

                        foreach ($task_document as $key => $value) {
                            $insert_document = array();
                            $insert_document['taskid'] = $loops_task_id;
                            $insert_document['document'] = $value->document;
                            $insert_document['description'] = $value->description;
                            $insert_document['createddate'] = getCurrentDate();
                            $insert_document['createdby'] = getCurrentIdUser();

                            $this->taskdocument->insert($insert_document);
                        }

                        $message = "Hallo, " . $operator->name . "!\n\n";
                        $message .= "Anda mendapatkan penugasan baru dengan detail sebagai berikut:\n\n";
                        $message .= "Tugas: " . $row->task . "\n";
                        $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
                        $message .= "Keterangan: " . $row->description . "\n\n";
                        $message .= "Silahkan login ke aplikasi untuk melihat detail penugasan.\n\n";
                        $message .= "Silahkan klik link berikut untuk login: " . base_url('auth/login') . "\n\n";
                        $message .= "Terima kasih.";

                        sendWABlas($operator->phonenumber, $message);

                        if (!in_array($operator->desaid, $desaid)) {
                            $desaid[] = $operator->desaid;
                        }
                    }

                    $get_notification = $this->msusers->where_in('a.desaid', $desaid)->get(array(
                        "(a.role = 'Kepala Desa' OR a.role = 'Villages') =" => true
                    ))->result();

                    foreach ($get_notification as $key => $value) {
                        if ($value->phonenumber == null) continue;

                        $message = "Hallo, " . $value->name . "!\n\n";
                        $message .= "Seluruh Operator " . $get_position->row()->name . " telah mendapatkan penugasan baru dengan detail sebagai berikut:\n\n";
                        $message .= "Tugas: " . $row->task . "\n";
                        $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
                        $message .= "Keterangan: " . $row->description . "\n\n";
                        $message .= "Silahkan login ke aplikasi untuk melihat detail penugasan.\n\n";
                        $message .= "Silahkan klik link berikut untuk login: " . base_url('auth/login') . "\n\n";
                        $message .= "Terima kasih.";

                        sendWABlas($value->phonenumber, $message);
                    }
                } else if ($row->taskfor == 'BPD') {
                    if ($row->created_role == 'DPMD') {
                        $get_bpd = $this->msusers->result(array(
                            'role' => 'BPD',
                            'kabkotaid' => $row->created_kabkotaid
                        ));
                    } else {
                        $get_bpd = $this->msusers->result(array(
                            'role' => 'BPD',
                            'kabkotaid' => $row->created_kabkotaid,
                            'kecamatanid' => $row->created_kecamatanid
                        ));
                    }

                    $desaid = array();
                    foreach ($get_bpd as $bpd) {
                        $insert = array();
                        $insert['deadline'] = $row->deadline;
                        $insert['task'] = $row->task;
                        $insert['to'] = $bpd->id;
                        $insert['description'] = $row->description;
                        $insert['createddate'] = date('Y-m-d H:i:s');
                        $insert['createdby'] = getCurrentIdUser();
                        $insert['taskfor'] = 'BPD';
                        $insert['is_release'] = 1;
                        $insert['status'] = 'Process';
                        $insert['parentid'] = $id;
                        $insert['starttask'] = $row->starttask;
                        $insert['document_sample'] = $row->document_sample;

                        $this->task->insert($insert);
                        $loops_task_id = $this->db->insert_id();

                        $task_document = $this->taskdocument->result(array(
                            'taskid' => $id
                        ));

                        foreach ($task_document as $key => $value) {
                            $insert_document = array();
                            $insert_document['taskid'] = $loops_task_id;
                            $insert_document['document'] = $value->document;
                            $insert_document['description'] = $value->description;
                            $insert_document['createddate'] = getCurrentDate();
                            $insert_document['createdby'] = getCurrentIdUser();

                            $this->taskdocument->insert($insert_document);
                        }

                        $message = "Hallo, " . $bpd->name . "!\n\n";
                        $message .= "Anda mendapatkan penugasan baru dengan detail sebagai berikut:\n\n";
                        $message .= "Tugas: " . $row->task . "\n";
                        $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
                        $message .= "Keterangan: " . $row->description . "\n\n";
                        $message .= "Silahkan login ke aplikasi untuk melihat detail penugasan.\n\n";
                        $message .= "Silahkan klik link berikut untuk login: " . base_url('auth/login') . "\n\n";
                        $message .= "Terima kasih.";

                        sendWABlas($bpd->phonenumber, $message);

                        if (!in_array($bpd->desaid, $desaid)) {
                            $desaid[] = $bpd->desaid;
                        }
                    }

                    $get_notification = $this->msusers->where_in('a.desaid', $desaid)->get(array(
                        "(a.role = 'Kepala Desa' OR a.role = 'Villages') =" => true
                    ))->result();

                    foreach ($get_notification as $key => $value) {
                        if ($value->phonenumber == null) continue;

                        $message = "Hallo, " . $value->name . "!\n\n";
                        $message .= "Seluruh BPD telah mendapatkan penugasan baru dengan detail sebagai berikut:\n\n";
                        $message .= "Tugas: " . $row->task . "\n";
                        $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
                        $message .= "Keterangan: " . $row->description . "\n\n";
                        $message .= "Silahkan login ke aplikasi untuk melihat detail penugasan.\n\n";
                        $message .= "Silahkan klik link berikut untuk login: " . base_url('auth/login') . "\n\n";
                        $message .= "Terima kasih.";

                        sendWABlas($value->phonenumber, $message);
                    }
                }
            }

            $this->task->update(array(
                'id' => $id
            ), array(
                'is_release' => 1,
                'status' => 'Process'
            ));

            $insert = array();
            $insert['taskid'] = $id;
            if (($row->created_role != 'DPMD' && $row->created_role != 'Kecamatan') && $row->taskfor == 'Operator Desa') {
                $insert['activity'] = 'Merelease tugas kepada ' . $operator->name . ' dengan detail sebagai berikut: ' . $row->task . ' dengan deadline ' . date('d F Y', strtotime($row->deadline)) . ' dan keterangan ' . $row->description;
            } else if ($row->taskfor == 'Operator Desa') {
                $insert['activity'] = 'Merelease tugas kepada Seluruh ' . $get_position->row()->name . ' dengan detail sebagai berikut: ' . $row->task . ' dengan deadline ' . date('d F Y', strtotime($row->deadline)) . ' dan keterangan ' . $row->description;
            } else if ($row->taskfor == 'BPD') {
                $insert['activity'] = 'Merelease tugas kepada Seluruh BPD dengan detail sebagai berikut: ' . $row->task . ' dengan deadline ' . date('d F Y', strtotime($row->deadline)) . ' dan keterangan ' . $row->description;
            }
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->taskhistory->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal di release!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Penugasan berhasil di release!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function collect()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $id = getPost('id');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID tidak boleh kosong!');
        }

        $get = $this->task->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penugasan tidak ditemukan!');
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('penugasan/collect', array(
                'task' => $get->row()
            ), true)
        ));
    }

    public function process_collect()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $id = getPost('id');
        $collectdescription = getPost('collectdescription', []);
        $document = isset($_FILES['document']['size'][0]) && $_FILES['document']['size'][0] > 0 ? $_FILES['document'] : null;

        if ($document == null) {
            return JSONResponseDefault('FAILED', 'Dokumen tidak boleh kosong!');
        } else if ($collectdescription == null) {
            return JSONResponseDefault('FAILED', 'Keterangan tidak boleh kosong!');
        }

        $get = $this->task->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penugasan tidak ditemukan!');
        }

        $config = array();
        $config['upload_path'] = './uploads/';
        $config['allowed_types'] = 'pdf|doc|docx|jpg|jpeg|png|xls|xlsx';
        $config['encrypt_name'] = true;

        $this->load->library('upload', $config);

        $documentfile = array();
        foreach ($document['name'] as $key => $name) {
            $_FILES['userfile']['name'] = $document['name'][$key];
            $_FILES['userfile']['type'] = $document['type'][$key];
            $_FILES['userfile']['tmp_name'] = $document['tmp_name'][$key];
            $_FILES['userfile']['error'] = $document['error'][$key];
            $_FILES['userfile']['size'] = $document['size'][$key];

            if ($this->upload->do_upload('userfile')) {
                $upload_data = $this->upload->data();

                $insert = array();
                $insert['taskid'] = $id;
                $insert['document'] = $upload_data['file_name'];
                $insert['description'] = $collectdescription[$key];
                $insert['createddate'] = date('Y-m-d H:i:s');
                $insert['createdby'] = getCurrentIdUser();

                $this->taskcollect->insert($insert);

                $documentfile[] = $upload_data['file_name'];
            }
        }

        $row = $get->row();
        $operator_desa = $this->msusers->get(array('id' => $row->to))->row();
        $kepala_desa = $this->msusers->get(array(
            'role' => 'Kepala Desa',
            'desaid' => $operator_desa->desaid
        ))->row();

        if ($kepala_desa != null) {
            $message = "Hallo, " . $kepala_desa->name . "!\n\n";
            $message .= "Operator " . $operator_desa->name . " telah mengumpulkan dokumen penugasan dengan detail sebagai berikut:\n\n";
            $message .= "Tugas: " . $row->task . "\n";
            $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
            $message .= "Keterangan: " . $row->description . "\n\n";
            $message .= "Silahkan login ke aplikasi untuk melakukan verifikasi.\n\n";
            $message .= "Silahkan klik link berikut untuk login: " . base_url('auth/login') . "\n\n";
            $message .= "Terima kasih.";

            sendWABlas($kepala_desa->phonenumber, $message);
        }

        $this->task->update(array(
            'id' => $id
        ), array(
            'status' => 'Pending',
            'collectdate' => date('Y-m-d H:i:s'),
        ));

        $documentinfo = "";
        foreach ($documentfile as $file) {
            $documentinfo .= "<a href='" . base_url('uploads/' . $file) . "' target='_blank'>" . $file . "</a><br>";
        }

        $insert = array();
        $insert['taskid'] = $id;
        $insert['activity'] = 'Mengumpulkan dokumen penugasan dengan detail sebagai berikut: ' . $row->task . ' dengan deadline ' . date('d F Y', strtotime($row->deadline)) . ' dan keterangan ' . $row->description . ' dengan dokumen sebagai berikut:<br>' . $documentinfo;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->taskhistory->insert($insert);

        if ($row->taskfor == 'BPD') {
            $desaid = $operator_desa->desaid;
            $task = $this->task->select('a.*')
                ->join('msusers b', 'b.id = a.to')
                ->result(array(
                    'a.taskfor' => 'BPD',
                    'a.parentid' => $row->parentid,
                    'b.desaid' => $desaid
                ));

            foreach ($task as $t) {
                $this->task->update(array(
                    'id' => $t->id
                ), array(
                    'status' => 'Pending',
                    'collectdate' => date('Y-m-d H:i:s'),
                ));
            }
        }

        return JSONResponseDefault('OK', 'Dokumen berhasil di upload!');
    }

    public function process_verify()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $id = getPost('id');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID tidak boleh kosong!');
        }

        $get = $this->task->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penugasan tidak ditemukan!');
        }

        $row = $get->row();
        $operator_desa = $this->msusers->get(array('id' => $row->to))->row();

        $message = "Hallo, " . $operator_desa->name . "!\n\n";
        $message .= "Penugasan dengan detail sebagai berikut:\n\n";
        $message .= "Tugas: " . $row->task . "\n";
        $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
        $message .= "Keterangan: " . $row->description . "\n\n";
        $message .= "Telah diverifikasi.\n\n";
        $message .= "Terima kasih.";

        sendWABlas($operator_desa->phonenumber, $message);

        $this->task->update(array(
            'id' => $id
        ), array(
            'status' => 'Done',
            'verifyid' => getCurrentIdUser(),
            'verifydate' => date('Y-m-d H:i:s')
        ));

        $insert = array();
        $insert['taskid'] = $id;
        $insert['activity'] = 'Melakukan verifikasi penugasan dengan detail sebagai berikut: ' . $row->task . ' dengan deadline ' . date('d F Y', strtotime($row->deadline)) . ' dan keterangan ' . $row->description;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->taskhistory->insert($insert);

        if ($operator_desa->role == 'BPD') {
            $task = $this->task->select('a.*')
                ->join('msusers b', 'b.id = a.to')
                ->result(array(
                    'a.taskfor' => 'BPD',
                    'b.desaid' => $operator_desa->desaid,
                    'b.id !=' => $operator_desa->id,
                ));

            foreach ($task as $t) {
                $this->task->update(array(
                    'id' => $t->id
                ), array(
                    'status' => 'Done',
                    'verifyid' => getCurrentIdUser(),
                    'verifydate' => date('Y-m-d H:i:s')
                ));
            }
        }

        return JSONResponseDefault('OK', 'Penugasan berhasil diverifikasi!');
    }

    public function process_reject()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $id = getPost('id');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID tidak boleh kosong!');
        }

        $get = $this->task->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penugasan tidak ditemukan!');
        }

        $row = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('penugasan/reject', array(
                'task' => $row
            ), true)
        ));
    }

    public function process_reject_task()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $id = getPost('id');
        $reason = getPost('reason');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID tidak boleh kosong!');
        } else if ($reason == null) {
            return JSONResponseDefault('FAILED', 'Alasan tidak boleh kosong!');
        }

        $get = $this->task->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penugasan tidak ditemukan!');
        }

        $row = $get->row();
        $operator_desa = $this->msusers->get(array('id' => $row->to))->row();

        $message = "Hallo, " . $operator_desa->name . "!\n\n";
        $message .= "Penugasan dengan detail sebagai berikut:\n\n";
        $message .= "Tugas: " . $row->task . "\n";
        $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
        $message .= "Keterangan: " . $row->description . "\n\n";
        $message .= "Ditolak dengan alasan: " . $reason . "\n\n";
        $message .= "Terima kasih.";

        sendWABlas($operator_desa->phonenumber, $message);

        $this->task->update(array(
            'id' => $id
        ), array(
            'status' => 'Reject',
            'reason' => $reason
        ));

        $insert = array();
        $insert['taskid'] = $id;
        $insert['activity'] = 'Menolak penugasan dengan detail sebagai berikut: ' . $row->task . ' dengan deadline ' . date('d F Y', strtotime($row->deadline)) . ' dan keterangan ' . $row->description . ' dengan alasan ' . $reason;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->taskhistory->insert($insert);

        if ($operator_desa->role == 'BPD') {
            $task = $this->task->select('a.*')
                ->join('msusers b', 'b.id = a.to')
                ->result(array(
                    'a.taskfor' => 'BPD',
                    'b.desaid' => $operator_desa->desaid,
                    'b.id !=' => $operator_desa->id,
                ));

            foreach ($task as $t) {
                $this->task->update(array(
                    'id' => $t->id
                ), array(
                    'status' => 'Reject',
                    'reason' => $reason
                ));
            }
        }

        return JSONResponseDefault('OK', 'Penugasan berhasil ditolak!');
    }

    public function history()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $id = getPost('id');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID tidak boleh kosong!');
        }

        $get = $this->taskhistory->select('a.*, b.role, b.kabkotaname, b.kecamatanname, b.desaname')
            ->join('msusers b', 'b.id = a.createdby')
            ->get(array(
                'a.taskid' => $id
            ));

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('penugasan/history', array(
                'histories' => $get->result()
            ), true)
        ));
    }

    public function process_task()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $id = getPost('id');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID tidak boleh kosong!');
        }

        $get = $this->task->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penugasan tidak ditemukan!');
        }

        $row = $get->row();

        $update = array();
        $update['status'] = 'Processing';

        if ($row->taskfor == 'BPD') {
            $update['processby'] = getCurrentIdUser();
        }

        $this->task->update(array(
            'id' => $id
        ), $update);

        $insert = array();
        $insert['taskid'] = $id;
        $insert['activity'] = 'Melakukan proses pengerjaan pada tugas';
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->taskhistory->insert($insert);

        if ($row->taskfor == 'BPD' && $row->parentid != null) {
            $operator_desa = $this->msusers->get(array('id' => $row->to))->row();
            $desaid = $operator_desa->desaid;

            $task = $this->task->select('a.*')
                ->join('msusers b', 'b.id = a.to')
                ->result(array(
                    'a.taskfor' => 'BPD',
                    'b.desaid' => $desaid,
                    'b.id !=' => $operator_desa->id,
                    'a.parentid' => $row->parentid
                ));

            foreach ($task as $key => $value) {
                $this->task->update(array(
                    'id' => $value->id
                ), array(
                    'status' => 'Processing',
                    'processby' => getCurrentIdUser()
                ));

                $insert = array();
                $insert['taskid'] = $value->id;
                $insert['activity'] = 'Tugas telah diambil oleh ' . $operator_desa->name . ' dengan username ' . $operator_desa->username . ' dan sedang dalam proses pengerjaan';
                $insert['createddate'] = getCurrentDate();
                $insert['createdby'] = getCurrentIdUser();

                $this->taskhistory->insert($insert);
            }
        }

        return JSONResponseDefault('OK', 'Penugasan berhasil diproses!');
    }

    public function detail($from = null, $id = null)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $kecamatanid = getGet('kecamatanid');
        $desaid = getGet('desaid');

        if ($id == null) {
            $id = $from;
        }

        $get = $this->task->select('a.*, b.role AS createdrole, b.kabkotaid, b.kecamatanname, b.kecamatanid, b.kabkotaname, b.desaname, IFNULL(e.name, f.name) AS positionname, c.name AS operator_name')
            ->join('msusers b', 'b.id = a.createdby')
            ->join('msusers c', 'c.id = a.to', 'LEFT')
            ->join('jabatan e', 'e.id = b.positionid', 'LEFT')
            ->join('jabatan f', 'f.id = a.to', 'LEFT')
            ->get(array(
                'a.id' => $id
            ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('penugasan'));
        }

        $data = array();
        $data['title'] = 'Detail Penugasan';
        $data['content'] = 'penugasan/detail';
        $data['task'] = $get->row();
        $data['history'] = $this->taskhistory->select('a.*')
            ->order_by('a.createddate', 'ASC')
            ->result(array(
                'a.taskid' => $id
            ));

        if ($get->row()->createdrole == 'DPMD') {
            $data['total_villages'] = getTotalVillagesByCity($get->row()->kabkotaname);
        } else if ($get->row()->createdrole == 'Kecamatan') {
            $data['total_villages'] = getTotalVillagesByDistrict($get->row()->kecamatanname);
        }

        if ($get->row()->taskfor == 'BPD') {
            $data['tocount'] = $this->task->select('b.desaid, b.kecamatanname, b.kabkotaname')
                ->join('msusers b', 'b.id = a.to')
                ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                ->total(array(
                    'parentid' => $get->row()->id
                ));
            $data['tocount_process'] = $this->task->select('b.desaid, b.kecamatanname, b.kabkotaname')
                ->join('msusers b', 'b.id = a.to')
                ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                ->total(array(
                    'parentid' => $get->row()->id,
                    'status' => 'Process'
                ));
            $data['tocount_processing'] = $this->task->select('b.desaid, b.kecamatanname, b.kabkotaname')
                ->join('msusers b', 'b.id = a.to')
                ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                ->total(array(
                    'parentid' => $get->row()->id,
                    'status' => 'Processing'
                ));
            $data['tocount_pending'] = $this->task->select('b.desaid, b.kecamatanname, b.kabkotaname')
                ->join('msusers b', 'b.id = a.to')
                ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                ->total(array(
                    'parentid' => $get->row()->id,
                    'status' => 'Pending'
                ));
            $data['tocount_done'] = $this->task->select('b.desaid, b.kecamatanname, b.kabkotaname')
                ->join('msusers b', 'b.id = a.to')
                ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                ->total(array(
                    'parentid' => $get->row()->id,
                    'status' => 'Done'
                ));
            $data['tocount_reject'] = $this->task->select('b.desaid, b.kecamatanname, b.kabkotaname')
                ->join('msusers b', 'b.id = a.to')
                ->group_by('b.desaid, b.kecamatanname, b.kabkotaname')
                ->total(array(
                    'parentid' => $get->row()->id,
                    'status' => 'Reject'
                ));
        } else {
            $data['tocount'] = $this->task->join('msusers b', 'b.id = a.to')->total(array(
                'parentid' => $get->row()->id
            ));
            $data['tocount_process'] = $this->task->join('msusers b', 'b.id = a.to')->total(array(
                'parentid' => $get->row()->id,
                'status' => 'Process'
            ));
            $data['tocount_processing'] = $this->task->join('msusers b', 'b.id = a.to')->total(array(
                'parentid' => $get->row()->id,
                'status' => 'Processing'
            ));
            $data['tocount_pending'] = $this->task->join('msusers b', 'b.id = a.to')->total(array(
                'parentid' => $get->row()->id,
                'status' => 'Pending'
            ));
            $data['tocount_done'] = $this->task->join('msusers b', 'b.id = a.to')->total(array(
                'parentid' => $get->row()->id,
                'status' => 'Done'
            ));
            $data['tocount_reject'] = $this->task->join('msusers b', 'b.id = a.to')->total(array(
                'parentid' => $get->row()->id,
                'status' => 'Reject'
            ));
        }

        $where_taskchild = array(
            'a.parentid' => $get->row()->id,
        );

        if ($kecamatanid != null) {
            $where_taskchild['b.kecamatanid'] = $kecamatanid;
        }

        if ($desaid != null) {
            $where_taskchild['b.desaid'] = $desaid;
        }

        if ($get->row()->taskfor == 'BPD') {
            $data['taskchild'] = $this->task->select('a.id, a.status, a.task, b.desaname, b.kecamatanname, b.kabkotaname')
                ->join('msusers b', 'b.id = a.to')
                ->join('jabatan c', 'c.id = b.positionid', 'LEFT')
                ->group_by('a.status, a.task, b.desaname, b.kecamatanname, b.kabkotaname')
                ->order_by('a.collectdate', 'DESC')
                ->result($where_taskchild);
        } else {
            $data['taskchild'] = $this->task->select('a.*, b.name AS operator_name, b.desaname, b.kecamatanname, b.kabkotaname, c.name as positionname')
                ->join('msusers b', 'b.id = a.to')
                ->join('jabatan c', 'c.id = b.positionid', 'LEFT')
                ->order_by('a.collectdate', 'DESC')
                ->result($where_taskchild);
        }

        if (isKecamatan()) {
            $data['kecamatan'] = $this->msusers->select('a.kecamatanid, a.kecamatanname')
                ->group_by('a.kecamatanid, a.kecamatanname')
                ->result(array(
                    'a.kecamatanname !=' => null,
                    'a.kecamatanid' => $get->row()->kecamatanid
                ));
        } else {
            $data['kecamatan'] = $this->msusers->select('a.kecamatanid, a.kecamatanname')
                ->group_by('a.kecamatanid, a.kecamatanname')
                ->result(array(
                    'a.kecamatanname !=' => null,
                    'a.kabkotaid' => $get->row()->kabkotaid
                ));
        }

        $data['kecamatanid'] = $kecamatanid;
        $data['desaid'] = $desaid;

        return $this->load->view('master', $data);
    }

    public function switch_task()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        } else if (!isKepalaDesa()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $id = getPost('id');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID tidak boleh kosong!');
        }

        $get = $this->task->get(array(
            'id' => $id
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penugasan tidak ditemukan!');
        }

        $row = $get->row();
        $currentuser = getCurrentUser();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('penugasan/switch', array(
                'task' => $row,
                'operator' => $this->operators->select('a.*, IFNULL(b.name, a.position) as positionname')
                    ->join('jabatan b', 'b.id = a.positionid', 'LEFT')
                    ->result(array(
                        'a.role' => 'Operator',
                        'a.id !=' => $row->to,
                        'a.desaid' => $currentuser->desaid
                    ))
            ), true)
        ));
    }

    public function process_switch_task()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        } else if (!isKepalaDesa()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $id = getPost('id');
        $operatorid = getPost('operatorid');

        if ($id == null) {
            return JSONResponseDefault('FAILED', 'ID tidak boleh kosong!');
        } else if ($operatorid == null) {
            return JSONResponseDefault('FAILED', 'Operator tidak boleh kosong!');
        }

        $get = $this->task->select('a.*, b.name AS operator_name, IFNULL(c.name, b.position) AS positionname')
            ->join('msusers b', 'b.id = a.to')
            ->join('jabatan c', 'c.id = b.positionid', 'LEFT')
            ->get(array(
                'a.id' => $id
            ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Penugasan tidak ditemukan!');
        }

        $row = $get->row();

        $operator = $this->operators->select('a.*, IFNULL(b.name, a.position) as positionname')
            ->join('jabatan b', 'b.id = a.positionid', 'LEFT')
            ->get(array(
                'a.id' => $operatorid
            ));

        if ($operator->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Operator tidak ditemukan!');
        }

        $operator = $operator->row();

        $this->task->update(array(
            'id' => $id
        ), array(
            'to' => $operatorid
        ));

        $insert = array();
        $insert['taskid'] = $id;
        $insert['activity'] = 'Mengganti operator dari ' . $row->operator_name . ' (' . $row->positionname . ') menjadi ' . $operator->name . ' (' . $operator->positionname . ')';
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $this->taskhistory->insert($insert);

        $message = "Hallo, " . $operator->name . "!\n\n";
        $message .= "Anda mendapatkan penugasan baru dengan detail sebagai berikut:\n\n";
        $message .= "Tugas: " . $row->task . "\n";
        $message .= "Deadline: " . date('d F Y', strtotime($row->deadline)) . "\n";
        $message .= "Keterangan: " . $row->description . "\n\n";
        $message .= "Silahkan login ke aplikasi untuk melihat detail penugasan.\n\n";
        $message .= "Silahkan klik link berikut untuk login: " . base_url('auth/login') . "\n\n";
        $message .= "Terima kasih.";

        sendWABlas($operator->phonenumber, $message);

        return JSONResponseDefault('OK', 'Operator berhasil diganti!');
    }

    public function select_villages()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $kecamatanid = getPost('kecamatanid');
        $desaid = getPost('desaid');

        $villages = $this->msusers->select('a.desaid, a.desaname')
            ->group_by('a.desaid, a.desaname')
            ->result(array(
                'a.kecamatanid' => $kecamatanid,
                'a.desaid !=' => null
            ));

        $select = "<option value=''>- Pilih -</option>";
        foreach ($villages as $village) {
            if ($village->desaid != $desaid) {
                $select .= "<option value='" . $village->desaid . "'>" . $village->desaname . "</option>";
            } else {
                $select .= "<option value='" . $village->desaid . "' selected>" . $village->desaname . "</option>";
            }
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $select
        ));
    }

    public function detail_status()
    {
        if (!isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda belum login!');
        }

        $id = getPost('id');
        $status = getPost('status');
        $currentuser = getCurrentUser();

        if ($status == 'Process') {
            $current_task = $this->task->select('b.role AS created_role, b.kecamatanname')
                ->join('msusers b', 'b.id = a.createdby')
                ->get(array(
                    'a.id' => $id
                ))->row();

            $tasks = $this->task->select('b.desaid')
                ->join('msusers b', 'b.id = a.to')
                ->result(array(
                    'a.parentid' => $id,
                    'a.status !=' => 'Process'
                ));

            if ($currentuser->role == 'DPMD') {
                if ($current_task->created_role == 'Kecamatan') {
                    $task = $this->msusers->select('a.desaname, a.kecamatanname, a.kabkotaname')
                        ->group_by('a.desaname, a.kecamatanname, a.kabkotaname')
                        ->where_not_in('a.desaid', array_column($tasks, 'desaid'))
                        ->result(array(
                            'a.role' => 'Villages',
                            'a.kabkotaname' => $currentuser->kabkotaname,
                            'a.kecamatanname' => $current_task->kecamatanname,
                        ));
                } else {
                    $task = $this->msusers->select('a.desaname, a.kecamatanname, a.kabkotaname')
                        ->group_by('a.desaname, a.kecamatanname, a.kabkotaname')
                        ->where_not_in('a.desaid', array_column($tasks, 'desaid'))
                        ->result(array(
                            'a.role' => 'Villages',
                            'a.kabkotaname' => $currentuser->kabkotaname,
                        ));
                }
            } else if ($currentuser->role == 'Kecamatan') {
                $task = $this->msusers->select('a.desaname, a.kecamatanname, a.kabkotaname')
                    ->group_by('a.desaname, a.kecamatanname, a.kabkotaname')
                    ->where_not_in('a.desaid', array_column($tasks, 'desaid'))
                    ->result(array(
                        'a.role' => 'Villages',
                        'a.kecamatanname' => $currentuser->kecamatanname,
                    ));
            }
        } else {
            $task = $this->task->select('b.desaname, b.kecamatanname, b.kabkotaname')
                ->join('msusers b', 'b.id = a.to')
                ->group_by('b.desaname, b.kecamatanname, b.kabkotaname')
                ->result(array(
                    'a.parentid' => $id,
                    'a.status' => $status
                ));
        }

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('penugasan/detail_status', array(
                'task' => $task,
                'status' => $status,
            ), true)
        ));
    }
}
