<!doctype html>
<html lang="en">
<!-- [Head] start -->

<head>
    <title><?= $title ?? 'Sistem Informasi Administrasi dan <PERSON>' ?></title>
    <!-- [Meta] -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
        name="description"
        content="Light Able admin and dashboard template offer a variety of UI elements and pages, ensuring your admin panel is both fast and effective." />
    <meta name="author" content="phoenixcoded" />

    <!-- [Favicon] icon -->
    <link rel="icon" href="<?= base_url('able') ?>/images/favicon.svg" type="image/x-icon" />
    <!-- [Google Font : Public Sans] icon -->
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@400;500;600;700&amp;display=swap" rel="stylesheet" />
    <!-- [phosphor Icons] https://phosphoricons.com/ -->
    <link rel="stylesheet" href="<?= base_url('able') ?>/fonts/phosphor/duotone/style.css" />
    <!-- [Tabler Icons] https://tablericons.com -->
    <link rel="stylesheet" href="<?= base_url('able') ?>/fonts/tabler-icons.min.css" />
    <!-- [Feather Icons] https://feathericons.com -->
    <link rel="stylesheet" href="<?= base_url('able') ?>/fonts/feather.css" />
    <!-- [Font Awesome Icons] https://fontawesome.com/icons -->
    <link rel="stylesheet" href="<?= base_url('able') ?>/fonts/fontawesome.css" />
    <!-- [Material Icons] https://fonts.google.com/icons -->
    <link rel="stylesheet" href="<?= base_url('able') ?>/fonts/material.css" />
    <!-- [Template CSS Files] -->
    <link rel="stylesheet" href="<?= base_url('able') ?>/css/style.css" id="main-style-link" />
    <link rel="stylesheet" href="<?= base_url('able') ?>/css/style-preset.css" />
    <link rel="stylesheet" href="<?= base_url('able') ?>/css/plugins/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/3.0.4/css/responsive.bootstrap5.css">
    <link rel="stylesheet" href="<?= base_url() ?>node_modules/bootstrap-multiselect-1.1.2/dist/css/bootstrap-multiselect.min.css">
    <link rel="stylesheet" href="<?= base_url() ?>node_modules/select2/dist/css/select2.min.css">

    <style>
        .swal-button--cancel:hover {
            background-color: #e8e8e8 !important;
        }

        .swal-button--danger:not([disabled]):hover {
            background-color: #f32b1e !important;
        }

        .loader {
            background-color: #fff !important;
        }

        .swal-text {
            text-align: center;
        }
    </style>
</head>
<!-- [Head] end -->
<!-- [Body] Start -->

<body data-pc-preset="preset-1" data-pc-sidebar-theme="light" data-pc-sidebar-caption="true" data-pc-direction="ltr" data-pc-theme="light">
    <!-- [ Pre-loader ] start -->
    <div class="loader-bg">
        <div class="loader-track">
            <div class="loader-fill"></div>
        </div>
    </div>
    <!-- [ Pre-loader ] End -->

    <div class="loader" style="display: flex; opacity: 1;">
        <div class="p-4 text-center">
            <div class="custom-loader"></div>
            <h2 class="my-3 f-w-400">Loading..</h2>
            <p class="mb-0">Please wait while we get your information from the web</p>
        </div>
    </div>

    <!-- [ Sidebar Menu ] start -->
    <nav class="pc-sidebar">
        <div class="navbar-wrapper">
            <div class="m-header justify-content-center mt-3">
                <a href="<?= base_url() ?>" class="b-brand text-primary">
                    <!-- ========   Change your logo from here   ============ -->
                    <img src="<?= base_url('assets/img/siapkada.png') ?>" alt="" style="width: 100px;">
                </a>
            </div>
            <div class="navbar-content">
                <ul class="pc-navbar">
                    <li class="pc-item pc-caption">
                        <label data-i18n="Dashboard">Dashboard</label>
                        <i class="ph-duotone ph-gauge"></i>
                    </li>

                    <li class="pc-item">
                        <a href="<?= base_url('dashboard') ?>" class="pc-link">
                            <span class="pc-micon">
                                <i class="ph-duotone ph-gauge"></i>
                            </span>
                            <span class="pc-mtext" data-i18n="Dashboard">Dashboard</span>
                        </a>
                    </li>

                    <?php if (isVillages() || isOperator() || isKecamatan() || isPMD() || isKepalaDesa() || isBPD()): ?>
                        <?php if (isVillages() || isKecamatan() || isPMD()): ?>
                            <?php if (!isPMD() && !isKecamatan()): ?>
                                <li class="pc-item pc-caption">
                                    <label data-i18n="Master">Master</label>
                                    <i class="ph-duotone ph-database"></i>
                                </li>
                            <?php else: ?>
                                <li class="pc-item pc-caption">
                                    <label data-i18n="Master">Monitoring Absensi</label>
                                    <i class="ph-duotone ph-monitor"></i>
                                </li>
                            <?php endif; ?>

                            <?php if (!isKecamatan() && !isPMD()): ?>
                                <li class="pc-item">
                                    <a href="<?= base_url('master/kepdes') ?>" class="pc-link">
                                        <span class="pc-micon">
                                            <i class="ph-duotone ph-user"></i>
                                        </span>
                                        <span class="pc-mtext" data-i18n="Kepala Desa">Kepala Desa</span>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <li class="pc-item">
                                <a href="<?= base_url('master/bpd') ?>" class="pc-link">
                                    <span class="pc-micon">
                                        <i class="ph-duotone ph-users"></i>
                                    </span>
                                    <span class="pc-mtext" data-i18n="BPD">BPD</span>
                                </a>
                            </li>

                            <li class="pc-item">
                                <a href="<?= base_url('master/operator') ?>" class="pc-link">
                                    <span class="pc-micon">
                                        <i class="ph-duotone ph-users"></i>
                                    </span>
                                    <span class="pc-mtext" data-i18n="Pemerintah Desa">Pemerintah Desa</span>
                                </a>
                            </li>

                            <?php if (isVillages()): ?>
                                <li class="pc-item">
                                    <a href="<?= base_url('integration') ?>" class="pc-link">
                                        <span class="pc-micon">
                                            <i class="ph-duotone ph-link"></i>
                                        </span>
                                        <span class="pc-mtext" data-i18n="Integrasi Siades">Integrasi Siades</span>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <li class="pc-item">
                                <a href="<?= base_url('master/user_guide') ?>" class="pc-link">
                                    <span class="pc-micon">
                                        <i class="ph-duotone ph-book"></i>
                                    </span>
                                    <span class="pc-mtext" data-i18n="Buku Panduan">Buku Panduan</span>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (isPMD() || isKecamatan() || isVillages()): ?>
                            <li class="pc-item pc-caption">
                                <label data-i18n="Event">Event/Rapat</label>
                                <i class="ph-duotone ph-calendar-blank"></i>
                            </li>

                            <li class="pc-item">
                                <a href="<?= base_url('event') ?>" class="pc-link">
                                    <span class="pc-micon">
                                        <i class="ph-duotone ph-calendar-blank"></i>
                                    </span>
                                    <span class="pc-mtext" data-i18n="Event">Event/Rapat</span>
                                </a>
                            </li>
                        <?php endif; ?>

                        <li class="pc-item pc-caption">
                            <label data-i18n="Penugasan">Penugasan</label>
                            <i class="ph-duotone ph-list"></i>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('penugasan/pmd') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-list"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Kabupaten">Kabupaten</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('penugasan/kecamatan') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-list"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Kecamatan">Kecamatan</span>
                            </a>
                        </li>

                        <?php if (!isBPD()): ?>
                            <li class="pc-item">
                                <a href="<?= base_url('penugasan/desa') ?>" class="pc-link">
                                    <span class="pc-micon">
                                        <i class="ph-duotone ph-list"></i>
                                    </span>
                                    <span class="pc-mtext" data-i18n="Desa">Desa</span>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (isVillages()): ?>
                            <li class="pc-item pc-caption">
                                <label data-i18n="Riwayat">Riwayat</label>
                                <i class="ph-duotone ph-list"></i>
                            </li>

                            <li class="pc-item">
                                <a href="<?= base_url('history/deletes') ?>" class="pc-link">
                                    <span class="pc-micon">
                                        <i class="ph-duotone ph-trash"></i>
                                    </span>
                                    <span class="pc-mtext" data-i18n="Riwayat Penghapusan">Riwayat Penghapusan</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if (isBPD() /* || isVillages()*/) : ?>
                        <li class="pc-item pc-caption">
                            <label data-i18n="Administrasi & Pembukuan">Administrasi & Pembukuan</label>
                            <i class="ph-duotone ph-book"></i>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/suratkeluar') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-envelope-open"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Agenda Surat Keluar">Agenda Surat Keluar</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/suratmasuk') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-envelope"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Agenda Surat Masuk">Agenda Surat Masuk</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/guestbook') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-book"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Buku Tamu BPD">Buku Tamu BPD</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/anggotabpd') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-users"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Anggota BPD">Anggota BPD</span>
                            </a>
                        </li>

                        <li class="pc-item pc-hasmenu">
                            <a href="javascript:;" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-clock-countdown"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Kegiatan BPD">Kegiatan BPD</span>
                                <span class="pc-arrow">
                                    <i data-feather="chevron-right"></i>
                                </span>
                            </a>

                            <ul class="pc-submenu">
                                <li class="pc-item">
                                    <a class="pc-link" href="<?= base_url('master/kegiatanbpd') ?>" data-i18n="Daftar Kegiatan BPD">Daftar Kegiatan BPD</a>
                                </li>

                                <li class="pc-item">
                                    <a href="<?= base_url('master/daftarhadir') ?>" class="pc-link" data-i18n="Daftar Hadir Kegiatan BPD">Daftar Hadir Kegiatan BPD</a>
                                </li>

                                <li class="pc-item">
                                    <a href="<?= base_url('master/notulenrapat') ?>" class="pc-link" data-i18n="Notulen Kegiatan">Notulen Kegiatan</a>
                                </li>
                            </ul>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/aspirasimasyarakat') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-users"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Aspirasi Masyarakat">Aspirasi Masyarakat</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/keputusanbpd') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-gear"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Peraturan/Keputusan BPD">Peraturan/Keputusan BPD</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/peraturandesa') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-gear"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Peraturan Desa">Peraturan Desa</span>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php if (isAdmin()) : ?>
                        <li class="pc-item pc-caption">
                            <label data-i18n="Master">Master</label>
                            <i class="ph-duotone ph-database"></i>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/superadmin') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-users"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Super Admin">Super Admin</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/pmd') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-users"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Dinas PMD">Dinas PMD</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/kecamatan') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-users"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Kecamatan">Kecamatan</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/jabatan') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-user"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Jabatan">Jabatan</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/village') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-users"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Desa">Desa</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/bpd') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-users"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="BPD">BPD</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('master/user_guide') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-book"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Buku Panduan">Buku Panduan</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('config/wablas') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-whatsapp-logo"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Config WABlas">Config WABlas</span>
                            </a>
                        </li>

                        <li class="pc-item">
                            <a href="<?= base_url('history/deletes') ?>" class="pc-link">
                                <span class="pc-micon">
                                    <i class="ph-duotone ph-trash"></i>
                                </span>
                                <span class="pc-mtext" data-i18n="Riwayat Penghapusan">Riwayat Penghapusan</span>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>

            <div class="card pc-user-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <img src="<?= base_url('able') ?>/images/user/avatar-1.jpg" alt="user-image" class="user-avtar wid-45 rounded-circle" />
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="dropdown">
                                <a href="#" class="arrow-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-offset="0,20">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1 me-2">
                                            <h6 class="mb-0"><?= getSessionValue('NAME') ?? getSessionValue('ROLE') ?></h6>
                                            <small>Administrator</small>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <div class="btn btn-icon btn-link-secondary avtar">
                                                <i class="ph-duotone ph-windows-logo"></i>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                                <div class="dropdown-menu">
                                    <ul>
                                        <li>
                                            <a class="pc-user-links" href="<?= base_url('account/profile') ?>">
                                                <i class="ph-duotone ph-user"></i>
                                                <span>My Account</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="pc-user-links" href="javascript:;" onclick="doLogout()">
                                                <i class="ph-duotone ph-power"></i>
                                                <span>Logout</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    <!-- [ Sidebar Menu ] end -->
    <!-- [ Header Topbar ] start -->
    <header class="pc-header">
        <div class="header-wrapper"> <!-- [Mobile Media Block] start -->
            <div class="me-auto pc-mob-drp">
                <ul class="list-unstyled">
                    <!-- ======= Menu collapse Icon ===== -->
                    <li class="pc-h-item pc-sidebar-collapse">
                        <a href="#" class="pc-head-link ms-0" id="sidebar-hide">
                            <i class="ti ti-menu-2"></i>
                        </a>
                    </li>
                    <li class="pc-h-item pc-sidebar-popup">
                        <a href="#" class="pc-head-link ms-0" id="mobile-collapse">
                            <i class="ti ti-menu-2"></i>
                        </a>
                    </li>
                </ul>
            </div>
            <!-- [Mobile Media Block end] -->
            <div class="ms-auto">
                <ul class="list-unstyled">
                    <li class="dropdown pc-h-item d-none d-md-inline-flex">
                        <a
                            class="pc-head-link dropdown-toggle arrow-none me-0"
                            data-bs-toggle="dropdown"
                            href="#"
                            role="button"
                            aria-haspopup="false"
                            aria-expanded="false">
                            <i class="ph-duotone ph-sun-dim"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end pc-h-dropdown">
                            <a href="#!" class="dropdown-item" onclick="layout_change('dark')">
                                <i class="ph-duotone ph-moon"></i>
                                <span>Dark</span>
                            </a>
                            <a href="#!" class="dropdown-item" onclick="layout_change('light')">
                                <i class="ph-duotone ph-sun-dim"></i>
                                <span>Light</span>
                            </a>
                            <a href="#!" class="dropdown-item" onclick="layout_change_default()">
                                <i class="ph-duotone ph-cpu"></i>
                                <span>Default</span>
                            </a>
                        </div>
                    </li>
                    <li class="pc-h-item">
                        <a class="pc-head-link pct-c-btn" href="#" data-bs-toggle="offcanvas" data-bs-target="#offcanvas_pc_layout">
                            <i class="ph-duotone ph-gear-six"></i>
                        </a>
                    </li>
                    <li class="dropdown pc-h-item">
                        <a
                            class="pc-head-link dropdown-toggle arrow-none me-0"
                            data-bs-toggle="dropdown"
                            href="#"
                            role="button"
                            aria-haspopup="false"
                            aria-expanded="false">
                            <i class="ph-duotone ph-diamonds-four"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end pc-h-dropdown">
                            <a href="<?= base_url('account/profile') ?>" class="dropdown-item">
                                <i class="ph-duotone ph-user"></i>
                                <span>My Account</span>
                            </a>
                            <a href="<?= base_url('account/password') ?>" class="dropdown-item">
                                <i class="ph-duotone ph-gear"></i>
                                <span>Change Password</span>
                            </a>
                            <a href="javascript:;" class="dropdown-item" onclick="doLogout()">
                                <i class="ph-duotone ph-power"></i>
                                <span>Logout</span>
                            </a>
                        </div>
                    </li>
                    <li class="dropdown pc-h-item header-user-profile">
                        <a
                            class="pc-head-link dropdown-toggle arrow-none me-0"
                            data-bs-toggle="dropdown"
                            href="#"
                            role="button"
                            aria-haspopup="false"
                            data-bs-auto-close="outside"
                            aria-expanded="false">
                            <img src="<?= base_url('able') ?>/images/user/avatar-2.jpg" alt="user-image" class="user-avtar" />
                        </a>
                        <div class="dropdown-menu dropdown-user-profile dropdown-menu-end pc-h-dropdown">
                            <div class="dropdown-header d-flex align-items-center justify-content-between">
                                <h5 class="m-0">Profile</h5>
                            </div>
                            <div class="dropdown-body">
                                <div class="profile-notification-scroll position-relative" style="max-height: calc(100vh - 225px)">
                                    <ul class="list-group list-group-flush w-100">
                                        <li class="list-group-item">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <img src="<?= base_url('able') ?>/images/user/avatar-2.jpg" alt="user-image" class="wid-50 rounded-circle" />
                                                </div>
                                                <div class="flex-grow-1 mx-3">
                                                    <h5 class="mb-0"><?= getSessionValue('NAME') ?? getSessionValue('ROLE') ?></h5>
                                                    <a class="link-primary" href="javascript:;">Administrator</a>
                                                </div>
                                            </div>
                                        </li>
                                        <li class="list-group-item">
                                            <a href="<?= base_url('account/profile') ?>" class="dropdown-item">
                                                <span class="d-flex align-items-center">
                                                    <i class="ph-duotone ph-user"></i>
                                                    <span>My Account</span>
                                                </span>
                                            </a>
                                            <a href="<?= base_url('account/password') ?>" class="dropdown-item">
                                                <span class="d-flex align-items-center">
                                                    <i class="ph-duotone ph-key"></i>
                                                    <span>Change password</span>
                                                </span>
                                            </a>
                                        </li>
                                        <li class="list-group-item">
                                            <a href="javascript:;" class="dropdown-item" onclick="doLogout()">
                                                <span class="d-flex align-items-center">
                                                    <i class="ph-duotone ph-power"></i>
                                                    <span>Logout</span>
                                                </span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </header>
    <!-- [ Header ] end -->



    <!-- [ Main Content ] start -->
    <div class="pc-container">
        <div class="pc-content">
            <?php if (isset($content)): ?>
                <?php $this->load->view($content) ?>
            <?php endif; ?>
        </div>
    </div>
    <!-- [ Main Content ] end -->
    <footer class="pc-footer">
        <div class="footer-wrapper container-fluid">
            <div class="row">
                <div class="col-sm-6 my-1">
                    <p class="m-0">Made with &#9829; by Team <a href="https://senusatech.com/" target="_blank"> CV Senusa Group</a></p>
                </div>
                <div class="col-sm-6 ms-auto my-1">
                    <ul class="list-inline footer-link mb-0 justify-content-sm-end d-flex">
                        <li class="list-inline-item"><a href="<?= base_url() ?>">Home</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <div class="modal fade" tabindex="-1" role="dialog" id="ModalGlobal">
    </div>

    <!-- Required Js -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="<?= base_url('able') ?>/js/plugins/popper.min.js"></script>
    <script src="<?= base_url('able') ?>/js/plugins/simplebar.min.js"></script>
    <script src="<?= base_url('able') ?>/js/plugins/bootstrap.min.js"></script>
    <script src="<?= base_url('able') ?>/js/plugins/i18next.min.js"></script>
    <script src="<?= base_url('able') ?>/js/plugins/i18nextHttpBackend.min.js"></script>
    <script src="<?= base_url('able') ?>/js/icon/custom-font.js"></script>
    <script src="<?= base_url('able') ?>/js/script.js"></script>
    <script src="<?= base_url('able') ?>/js/theme.js"></script>
    <script src="<?= base_url('able') ?>/js/multi-lang.js"></script>
    <script src="<?= base_url('able') ?>/js/plugins/feather.min.js"></script>
    <script src="<?= base_url() ?>node_modules/sweetalert/dist/sweetalert.min.js"></script>
    <script src="<?= base_url() ?>node_modules/bootstrap-multiselect-1.1.2/dist/js/bootstrap-multiselect.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js" integrity="sha512-ElRFoEQdI5Ht6kZvyzXhYG9NqjtkmlkfYk0wr6wHxU9JEHakS7UJZNeml5ALk+8IKlU6jDgMabC3vkumRokgJA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="<?= base_url('able') ?>/js/plugins/dataTables.min.js"></script>
    <script src="<?= base_url('able') ?>/js/plugins/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/3.0.4/js/dataTables.responsive.js"></script>
    <script src="<?= base_url() ?>node_modules/select2/dist/js/select2.full.min.js"></script>
    <script src="<?= base_url() ?>assets/js/script.js"></script>
    <script src="<?= base_url() ?>assets/js/ajax-request.js"></script>

    <?php if (isset($script)) : ?>
        <?php $this->load->view($script) ?>
    <?php endif; ?>

    <!-- Page Specific JS File -->
    <script>
        // toggle password visibility
        function togglePasswordVisibility(button_elm) {
            // find input field
            var input_field = $(button_elm).closest('.input-group').find('input');

            // toggle password visibility
            if (input_field.attr('type') == 'password') {
                input_field.attr('type', 'text');
                $(button_elm).find('i').removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                input_field.attr('type', 'password');
                $(button_elm).find('i').removeClass('fa-eye-slash').addClass('fa-eye');
            }
        }


        $('.datatables').dataTable({
            ordering: false,
            responsive: true
        });

        $.AjaxRequest('#frm', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.href = $('#frm').attr('success-redirect');
                    })
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });

        function deleteData(nama, id) {
            swal({
                title: 'Apakah anda yakin?',
                text: `${nama} akan dihapus secara permanen dari database`,
                icon: 'warning',
                buttons: true,
                dangerMode: true
            }).then(isAccepted => {
                if (isAccepted) {
                    $.ajax({
                        url: '<?= base_url(uri_string() . '/delete') ?>',
                        method: 'POST',
                        dataType: 'json',
                        data: {
                            id: id
                        },
                        success: function(response) {
                            if (response.RESULT == 'OK') {
                                return swalMessageSuccess(response.MESSAGE, ok => {
                                    return window.location.reload();
                                });
                            } else {
                                return swalMessageFailed(response.MESSAGE);
                            }
                        }
                    }).fail(function() {
                        return swalError();
                    })
                }
            })
        }

        function doLogout() {
            swal({
                title: 'Apakah anda yakin?',
                text: `Apakah anda yakin ingin keluar dari aplikasi?`,
                icon: 'warning',
                buttons: true,
                dangerMode: true
            }).then(isAccepted => {
                if (isAccepted) {
                    return window.location.href = '<?= base_url('auth/logout') ?>';
                }
            });
        }
    </script>

    <script>
        // if storage have layout, then get it but if not, set default layout
        if (localStorage.getItem('layout')) {
            layout_change(localStorage.getItem('layout'));
        } else {
            layout_change_default();
        }
    </script>

    <script>
        layout_sidebar_change('light');
    </script>

    <script>
        change_box_container('false');
    </script>

    <script>
        layout_caption_change('true');
    </script>

    <script>
        layout_rtl_change('false');
    </script>

    <script>
        preset_change('preset-1');
    </script>

    <div class="offcanvas border-0 pct-offcanvas offcanvas-end" tabindex="-1" id="offcanvas_pc_layout">
        <div class="offcanvas-header justify-content-between">
            <h5 class="offcanvas-title">Settings</h5>
            <button type="button" class="btn btn-icon btn-link-danger" data-bs-dismiss="offcanvas" aria-label="Close"><i class="ti ti-x"></i></button>
        </div>

        <div class="pct-body customizer-body">
            <div class="offcanvas-body py-0">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <div class="pc-dark">
                            <h6 class="mb-1">Theme Mode</h6>
                            <p class="text-muted text-sm">Choose light or dark mode or Auto</p>
                            <div class="row theme-color theme-layout">
                                <div class="col-4">
                                    <div class="d-grid">
                                        <button class="preset-btn btn active" data-value="true" onclick="layout_change('light');">
                                            <span class="btn-label">Light</span>
                                            <span class="pc-lay-icon"><span></span><span></span><span></span><span></span></span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="d-grid">
                                        <button class="preset-btn btn" data-value="false" onclick="layout_change('dark');">
                                            <span class="btn-label">Dark</span>
                                            <span class="pc-lay-icon"><span></span><span></span><span></span><span></span></span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="d-grid">
                                        <button
                                            class="preset-btn btn"
                                            data-value="default"
                                            onclick="layout_change_default();"
                                            data-bs-toggle="tooltip"
                                            title="Automatically sets the theme based on user's operating system's color scheme.">
                                            <span class="btn-label">Default</span>
                                            <span class="pc-lay-icon d-flex align-items-center justify-content-center">
                                                <i class="ph-duotone ph-cpu"></i>
                                            </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                    <li class="list-group-item pc-sidebar-color">
                        <h6 class="mb-1">Sidebar Theme</h6>
                        <p class="text-muted text-sm">Choose Sidebar Theme</p>
                        <div class="row theme-color theme-sidebar-color">
                            <div class="col-6">
                                <div class="d-grid">
                                    <button class="preset-btn btn" data-value="true" onclick="layout_sidebar_change('dark');">
                                        <span class="btn-label">Dark</span>
                                        <span class="pc-lay-icon"><span></span><span></span><span></span><span></span></span>
                                    </button>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-grid">
                                    <button class="preset-btn btn active" data-value="false" onclick="layout_sidebar_change('light');">
                                        <span class="btn-label">Light</span>
                                        <span class="pc-lay-icon"><span></span><span></span><span></span><span></span></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </li>

                    <li class="list-group-item">
                        <h6 class="mb-1">Accent color</h6>
                        <p class="text-muted text-sm">Choose your primary theme color</p>
                        <div class="theme-color preset-color">
                            <a href="#!" class="active" data-value="preset-1"><i class="ti ti-check"></i></a>
                            <a href="#!" data-value="preset-2"><i class="ti ti-check"></i></a>
                            <a href="#!" data-value="preset-3"><i class="ti ti-check"></i></a>
                            <a href="#!" data-value="preset-4"><i class="ti ti-check"></i></a>
                            <a href="#!" data-value="preset-5"><i class="ti ti-check"></i></a>
                            <a href="#!" data-value="preset-6"><i class="ti ti-check"></i></a>
                            <a href="#!" data-value="preset-7"><i class="ti ti-check"></i></a>
                            <a href="#!" data-value="preset-8"><i class="ti ti-check"></i></a>
                            <a href="#!" data-value="preset-9"><i class="ti ti-check"></i></a>
                            <a href="#!" data-value="preset-10"><i class="ti ti-check"></i></a>
                        </div>
                    </li>

                    <li class="list-group-item">
                        <h6 class="mb-1">Sidebar Caption</h6>
                        <p class="text-muted text-sm">Sidebar Caption Hide/Show</p>
                        <div class="row theme-color theme-nav-caption">
                            <div class="col-6">
                                <div class="d-grid">
                                    <button class="preset-btn btn active" data-value="true" onclick="layout_caption_change('true');">
                                        <span class="btn-label">Caption Show</span>
                                        <span class="pc-lay-icon"><span></span><span></span><span><span></span><span></span></span><span></span></span>
                                    </button>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-grid">
                                    <button class="preset-btn btn" data-value="false" onclick="layout_caption_change('false');">
                                        <span class="btn-label">Caption Hide</span>
                                        <span class="pc-lay-icon"><span></span><span></span><span><span></span><span></span></span><span></span></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </li>

                    <li class="list-group-item">
                        <div class="pc-rtl">
                            <h6 class="mb-1">Theme Layout</h6>
                            <p class="text-muted text-sm">LTR/RTL</p>
                            <div class="row theme-color theme-direction">
                                <div class="col-6">
                                    <div class="d-grid">
                                        <button class="preset-btn btn active" data-value="false" onclick="layout_rtl_change('false');">
                                            <span class="btn-label">LTR</span>
                                            <span class="pc-lay-icon"><span></span><span></span><span></span><span></span></span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-grid">
                                        <button class="preset-btn btn" data-value="true" onclick="layout_rtl_change('true');">
                                            <span class="btn-label">RTL</span>
                                            <span class="pc-lay-icon"><span></span><span></span><span></span><span></span></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                    <li class="list-group-item pc-box-width">
                        <div class="pc-container-width">
                            <h6 class="mb-1">Layout Width</h6>
                            <p class="text-muted text-sm">Choose Full or Container Layout</p>
                            <div class="row theme-color theme-container">
                                <div class="col-6">
                                    <div class="d-grid">
                                        <button class="preset-btn btn active" data-value="false" onclick="change_box_container('false')">
                                            <span class="btn-label">Full Width</span>
                                            <span class="pc-lay-icon"><span></span><span></span><span></span><span><span></span></span></span>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-grid">
                                        <button class="preset-btn btn" data-value="true" onclick="change_box_container('true')">
                                            <span class="btn-label">Fixed Width</span>
                                            <span class="pc-lay-icon"><span></span><span></span><span></span><span><span></span></span></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                    <li class="list-group-item">
                        <div class="d-grid">
                            <button class="btn btn-light-danger" id="layoutreset">Reset Layout</button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        window.addEventListener('load', function() {
            $('.loader').fadeOut();
        });
    </script>
</body>
<!-- [Body] end -->

</html>

</html>