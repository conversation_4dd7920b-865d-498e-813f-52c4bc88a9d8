<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!-- [ breadcrumb ] start -->
<div class="page-header">
    <div class="page-block">
        <div class="row align-items-center">
            <div class="col-md-12">
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item" aria-current="page">Daftar Hadir Kegiatan BPD</li>
                </ul>
            </div>

            <div class="col-md-12">
                <div class="page-header-title">
                    <h2 class="mb-0">Daftar Hadir Kegiatan BPD</h2>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ breadcrumb ] end -->

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between">
                <div>
                    <h4>Data Daftar Hadir Kegiatan BPD</h4>
                </div>

                <div>
                    <a href="<?= base_url(uri_string() . '/export') ?>" class="btn btn-danger" target="_blank">
                        <i class="fa fa-file"></i>
                        <span>Export</span>
                    </a>

                    <?php if (isBPD()): ?>
                        <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary">
                            <i class="fa fa-plus"></i>
                            <span>Tambah</span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped datatables">
                        <thead>
                            <tr>
                                <th>Dibuat Oleh</th>
                                <th>Jabatan</th>
                                <th>Nama</th>
                                <th>Jenis Kelamin</th>
                                <th>Alamat</th>
                                <th>Instansi / Desa</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php foreach ($daftarhadir as $key => $value) : ?>
                                <tr>
                                    <td><?= $value->createdname ?> - <?= $value->createdusername ?></td>
                                    <td><?= $value->jabatan ?></td>
                                    <td><?= $value->nama ?></td>
                                    <td><?= $value->gender ?></td>
                                    <td><?= $value->alamat ?? '-' ?></td>
                                    <td><?= $value->instansi ?></td>
                                    <td>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="showHistory('<?= $value->id ?>')">
                                            <i class="fa fa-history"></i>
                                            Riwayat Rapat
                                        </button>

                                        <?php if (isBPD()): ?>
                                            <a href="<?= base_url(uri_string() . '/edit/' . $value->id) ?>" class="btn btn-primary btn-sm">
                                                <i class="fa fa-edit"></i>
                                            </a>

                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteData('<?= $value->nama ?>', <?= $value->id ?>)">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function showHistory(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/history') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT).modal('show');
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            }
        }).fail(function() {
            return swalError();
        });
    }
</script>